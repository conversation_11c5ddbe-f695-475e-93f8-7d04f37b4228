2025-08-13 08:18:45.604 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 08:20:37.036 | INFO     | 1c0f6b90d5954598acbbd956843ae810 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:20:37.038 | INFO     | 92e58a644c2b490d91399f458b177702 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:20:37.039 | INFO     | 23f2684880584e5c8d8ca2a94d7b1b2e | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:20:37.042 | INFO     | 1c0f6b90d5954598acbbd956843ae810 | 成功认证Java用户: pythontest
2025-08-13 08:20:37.044 | INFO     | 92e58a644c2b490d91399f458b177702 | 成功认证Java用户: pythontest
2025-08-13 08:20:37.045 | INFO     | 23f2684880584e5c8d8ca2a94d7b1b2e | 成功认证Java用户: pythontest
2025-08-13 08:20:37.086 | INFO     | 1c0f6b90d5954598acbbd956843ae810 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 08:20:37.094 | INFO     | 92e58a644c2b490d91399f458b177702 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:20:37.095 | INFO     | 92e58a644c2b490d91399f458b177702 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 08:20:37.099 | INFO     | 23f2684880584e5c8d8ca2a94d7b1b2e | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:20:37.099 | INFO     | 23f2684880584e5c8d8ca2a94d7b1b2e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 08:20:37.101 | INFO     | 1c0f6b90d5954598acbbd956843ae810 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 80.757ms
2025-08-13 08:20:37.115 | INFO     | 92e58a644c2b490d91399f458b177702 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:20:37.116 | INFO     | 23f2684880584e5c8d8ca2a94d7b1b2e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:20:37.118 | INFO     | 23f2684880584e5c8d8ca2a94d7b1b2e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 80.584ms
2025-08-13 08:20:37.120 | INFO     | 92e58a644c2b490d91399f458b177702 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 82.655ms
2025-08-13 08:20:44.908 | INFO     | 1eb79d8689144580aa1802ad1a9e7b5f | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:20:44.909 | INFO     | 1eb79d8689144580aa1802ad1a9e7b5f | 成功认证Java用户: pythontest
2025-08-13 08:20:44.921 | INFO     | 1eb79d8689144580aa1802ad1a9e7b5f | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 08:20:44.922 | INFO     | 1eb79d8689144580aa1802ad1a9e7b5f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 14.995ms
2025-08-13 08:20:44.926 | INFO     | 8a0e0adcba234a78bc00a1830eb88329 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:20:44.928 | INFO     | 8a0e0adcba234a78bc00a1830eb88329 | 成功认证Java用户: pythontest
2025-08-13 08:20:44.952 | INFO     | 8a0e0adcba234a78bc00a1830eb88329 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:20:44.953 | INFO     | 8a0e0adcba234a78bc00a1830eb88329 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 08:20:44.965 | INFO     | 8a0e0adcba234a78bc00a1830eb88329 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:20:44.967 | INFO     | 8a0e0adcba234a78bc00a1830eb88329 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 40.367ms
2025-08-13 08:20:44.970 | INFO     | 79c12123248b468c81cf715b976ee8e5 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:20:44.971 | INFO     | 79c12123248b468c81cf715b976ee8e5 | 成功认证Java用户: pythontest
2025-08-13 08:20:44.975 | INFO     | 79c12123248b468c81cf715b976ee8e5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:20:44.976 | INFO     | 79c12123248b468c81cf715b976ee8e5 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 08:20:44.987 | INFO     | 79c12123248b468c81cf715b976ee8e5 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:20:44.988 | INFO     | 79c12123248b468c81cf715b976ee8e5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 18.498ms
2025-08-13 08:21:43.690 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 08:22:16.880 | INFO     | 88969994820a47b4b815e0f520b8017c | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:22:16.882 | INFO     | 9545dedbc8334cb7b38e1f2145608c51 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:22:16.884 | INFO     | f74bb740c6b04a948930c87a778103d4 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:22:16.886 | INFO     | 88969994820a47b4b815e0f520b8017c | 成功认证Java用户: pythontest
2025-08-13 08:22:16.888 | INFO     | 9545dedbc8334cb7b38e1f2145608c51 | 成功认证Java用户: pythontest
2025-08-13 08:22:16.889 | INFO     | f74bb740c6b04a948930c87a778103d4 | 成功认证Java用户: pythontest
2025-08-13 08:22:16.917 | INFO     | 9545dedbc8334cb7b38e1f2145608c51 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:22:16.918 | INFO     | 9545dedbc8334cb7b38e1f2145608c51 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 08:22:16.921 | INFO     | f74bb740c6b04a948930c87a778103d4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:22:16.921 | INFO     | f74bb740c6b04a948930c87a778103d4 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 08:22:16.924 | INFO     | 88969994820a47b4b815e0f520b8017c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 08:22:16.928 | INFO     | 88969994820a47b4b815e0f520b8017c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 52.851ms
2025-08-13 08:22:16.938 | INFO     | 9545dedbc8334cb7b38e1f2145608c51 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:22:16.940 | INFO     | 9545dedbc8334cb7b38e1f2145608c51 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 57.450ms
2025-08-13 08:22:16.941 | INFO     | f74bb740c6b04a948930c87a778103d4 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:22:16.942 | INFO     | f74bb740c6b04a948930c87a778103d4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 60.785ms
2025-08-13 08:22:33.331 | INFO     | 1822ca7283694b81a406ccdc34ed089c | JWT标准验证成功，获取UUID: 6b0bf206-4605-4ef3-903b-2775d59c73dc
2025-08-13 08:22:33.332 | INFO     | 712db901b5fa48c68e4a60025738873a | JWT标准验证成功，获取UUID: 6b0bf206-4605-4ef3-903b-2775d59c73dc
2025-08-13 08:22:33.332 | INFO     | 08295660fcc04262a4cfc4d1e5ae8385 | JWT标准验证成功，获取UUID: 6b0bf206-4605-4ef3-903b-2775d59c73dc
2025-08-13 08:22:33.342 | INFO     | 1822ca7283694b81a406ccdc34ed089c | 成功认证Java用户: admin
2025-08-13 08:22:33.344 | INFO     | 712db901b5fa48c68e4a60025738873a | 成功认证Java用户: admin
2025-08-13 08:22:33.345 | INFO     | 08295660fcc04262a4cfc4d1e5ae8385 | 成功认证Java用户: admin
2025-08-13 08:22:33.367 | INFO     | 08295660fcc04262a4cfc4d1e5ae8385 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-13 08:22:33.367 | INFO     | 08295660fcc04262a4cfc4d1e5ae8385 | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-13 08:22:33.372 | INFO     | 712db901b5fa48c68e4a60025738873a | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-13 08:22:33.372 | INFO     | 712db901b5fa48c68e4a60025738873a | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-13 08:22:33.376 | INFO     | 1822ca7283694b81a406ccdc34ed089c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 08:22:33.380 | INFO     | 1822ca7283694b81a406ccdc34ed089c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 48.632ms
2025-08-13 08:22:33.389 | INFO     | 08295660fcc04262a4cfc4d1e5ae8385 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:22:33.391 | INFO     | 712db901b5fa48c68e4a60025738873a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:22:33.392 | INFO     | 08295660fcc04262a4cfc4d1e5ae8385 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 60.498ms
2025-08-13 08:22:33.394 | INFO     | 712db901b5fa48c68e4a60025738873a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 61.682ms
2025-08-13 08:22:57.065 | INFO     | f483a436edda43a485a6240e5eaee2d8 | JWT标准验证成功，获取UUID: 6b0bf206-4605-4ef3-903b-2775d59c73dc
2025-08-13 08:22:57.065 | INFO     | f055c6e718e2456b96035aeef8c54a2b | JWT标准验证成功，获取UUID: 6b0bf206-4605-4ef3-903b-2775d59c73dc
2025-08-13 08:22:57.070 | INFO     | f483a436edda43a485a6240e5eaee2d8 | 成功认证Java用户: admin
2025-08-13 08:22:57.071 | INFO     | f055c6e718e2456b96035aeef8c54a2b | 成功认证Java用户: admin
2025-08-13 08:22:57.093 | INFO     | f055c6e718e2456b96035aeef8c54a2b | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-13 08:22:57.094 | INFO     | f055c6e718e2456b96035aeef8c54a2b | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-13 08:22:57.099 | INFO     | f483a436edda43a485a6240e5eaee2d8 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 08:22:57.099 | INFO     | f483a436edda43a485a6240e5eaee2d8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 35.403ms
2025-08-13 08:22:57.101 | INFO     | 33256abeabfa49968dcad469488277f1 | JWT标准验证成功，获取UUID: 6b0bf206-4605-4ef3-903b-2775d59c73dc
2025-08-13 08:22:57.103 | INFO     | 33256abeabfa49968dcad469488277f1 | 成功认证Java用户: admin
2025-08-13 08:22:57.106 | INFO     | f055c6e718e2456b96035aeef8c54a2b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:22:57.108 | INFO     | f055c6e718e2456b96035aeef8c54a2b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 42.993ms
2025-08-13 08:22:57.109 | INFO     | 33256abeabfa49968dcad469488277f1 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-13 08:22:57.110 | INFO     | 33256abeabfa49968dcad469488277f1 | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-13 08:22:57.123 | INFO     | 33256abeabfa49968dcad469488277f1 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:22:57.124 | INFO     | 33256abeabfa49968dcad469488277f1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 23.354ms
2025-08-13 08:23:40.978 | INFO     | b19f81fb5706491398bf2b47735a223b | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:23:40.980 | INFO     | 9165366dcee1496da26959047229742c | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:23:40.980 | INFO     | 6f90e154704c4bc0a23bf3f2ec47ba87 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:23:40.981 | INFO     | b19f81fb5706491398bf2b47735a223b | 成功认证Java用户: pythontest
2025-08-13 08:23:40.983 | INFO     | 9165366dcee1496da26959047229742c | 成功认证Java用户: pythontest
2025-08-13 08:23:40.983 | INFO     | 6f90e154704c4bc0a23bf3f2ec47ba87 | 成功认证Java用户: pythontest
2025-08-13 08:23:40.993 | INFO     | 6f90e154704c4bc0a23bf3f2ec47ba87 | 获取用户权限成功: user_id=42, is_admin=False, permissions=16
2025-08-13 08:23:40.994 | INFO     | 9165366dcee1496da26959047229742c | 获取用户权限成功: user_id=42, is_admin=False, permissions=16
2025-08-13 08:23:40.995 | INFO     | 9165366dcee1496da26959047229742c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 08:23:41.001 | INFO     | b19f81fb5706491398bf2b47735a223b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 08:23:41.004 | INFO     | 6f90e154704c4bc0a23bf3f2ec47ba87 | 127.0.0.1       | GET      | 403    | /api/iot/v1/knowledge-base/stats/overview | 24.063ms
2025-08-13 08:23:41.010 | INFO     | b19f81fb5706491398bf2b47735a223b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 31.538ms
2025-08-13 08:23:41.016 | INFO     | 9165366dcee1496da26959047229742c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:23:41.018 | INFO     | 9165366dcee1496da26959047229742c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 38.857ms
2025-08-13 08:23:54.547 | INFO     | 0468b0c6b32740919c92f2ea5b7d55b2 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:23:54.549 | INFO     | 0468b0c6b32740919c92f2ea5b7d55b2 | 成功认证Java用户: pythontest
2025-08-13 08:23:54.560 | INFO     | 0468b0c6b32740919c92f2ea5b7d55b2 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 08:23:54.561 | INFO     | 0468b0c6b32740919c92f2ea5b7d55b2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 14.140ms
2025-08-13 08:23:54.566 | INFO     | be4c98be764f4881822e929401f7ef66 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:23:54.567 | INFO     | be4c98be764f4881822e929401f7ef66 | 成功认证Java用户: pythontest
2025-08-13 08:23:54.574 | INFO     | be4c98be764f4881822e929401f7ef66 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:23:54.574 | INFO     | be4c98be764f4881822e929401f7ef66 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 08:23:54.586 | INFO     | be4c98be764f4881822e929401f7ef66 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:23:54.588 | INFO     | be4c98be764f4881822e929401f7ef66 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 21.306ms
2025-08-13 08:23:54.591 | INFO     | 895aa09eb368415bb7108b0e25eb46e7 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 08:23:54.592 | INFO     | 895aa09eb368415bb7108b0e25eb46e7 | 成功认证Java用户: pythontest
2025-08-13 08:23:54.597 | INFO     | 895aa09eb368415bb7108b0e25eb46e7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 08:23:54.597 | INFO     | 895aa09eb368415bb7108b0e25eb46e7 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 08:23:54.608 | INFO     | 895aa09eb368415bb7108b0e25eb46e7 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 08:23:54.610 | INFO     | 895aa09eb368415bb7108b0e25eb46e7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 19.116ms
2025-08-13 09:01:25.215 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:22:34.936 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:26:09.100 | INFO     | fb35c176d2f8408cbedfa6973c96e939 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:26:09.101 | INFO     | f1eb1f7d029e44af9e2f3760ca6316a7 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:26:09.102 | INFO     | 8ec5b8de6661464d8f4f709cb69b4967 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:26:09.106 | INFO     | fb35c176d2f8408cbedfa6973c96e939 | 成功认证Java用户: pythontest
2025-08-13 10:26:09.107 | INFO     | f1eb1f7d029e44af9e2f3760ca6316a7 | 成功认证Java用户: pythontest
2025-08-13 10:26:09.107 | INFO     | 8ec5b8de6661464d8f4f709cb69b4967 | 成功认证Java用户: pythontest
2025-08-13 10:26:09.152 | INFO     | 8ec5b8de6661464d8f4f709cb69b4967 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:26:09.153 | INFO     | 8ec5b8de6661464d8f4f709cb69b4967 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:26:09.157 | INFO     | f1eb1f7d029e44af9e2f3760ca6316a7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:26:09.158 | INFO     | f1eb1f7d029e44af9e2f3760ca6316a7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:26:09.637 | INFO     | 8ec5b8de6661464d8f4f709cb69b4967 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 502 Bad Gateway"
2025-08-13 10:26:09.653 | INFO     | 8ec5b8de6661464d8f4f709cb69b4967 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 550.094ms
2025-08-13 10:26:39.166 | INFO     | fb35c176d2f8408cbedfa6973c96e939 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 30077.600ms
2025-08-13 10:26:39.167 | INFO     | f1eb1f7d029e44af9e2f3760ca6316a7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30066.061ms
2025-08-13 10:26:58.932 | INFO     | fc3964eaae7844c99447747da66270da | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:26:58.933 | INFO     | d97da7bdf5d94b7496f7038cce8cb573 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:26:58.933 | INFO     | 39103bcadb9d43a7ab6a85a0546bfb70 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:26:58.934 | INFO     | fc3964eaae7844c99447747da66270da | 成功认证Java用户: pythontest
2025-08-13 10:26:58.934 | INFO     | d97da7bdf5d94b7496f7038cce8cb573 | 成功认证Java用户: pythontest
2025-08-13 10:26:58.935 | INFO     | 39103bcadb9d43a7ab6a85a0546bfb70 | 成功认证Java用户: pythontest
2025-08-13 10:26:58.956 | INFO     | 39103bcadb9d43a7ab6a85a0546bfb70 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:26:58.956 | INFO     | 39103bcadb9d43a7ab6a85a0546bfb70 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:26:58.960 | INFO     | d97da7bdf5d94b7496f7038cce8cb573 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:26:58.960 | INFO     | d97da7bdf5d94b7496f7038cce8cb573 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:27:28.976 | INFO     | 39103bcadb9d43a7ab6a85a0546bfb70 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30043.504ms
2025-08-13 10:27:28.976 | INFO     | fc3964eaae7844c99447747da66270da | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 30044.890ms
2025-08-13 10:27:28.977 | INFO     | d97da7bdf5d94b7496f7038cce8cb573 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30044.557ms
2025-08-13 10:27:41.378 | INFO     | 628e1552f7f142e4b7d316ab0bc7e803 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:27:41.379 | INFO     | 39ed89e0fb1e45738123572639cbfab3 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:27:41.380 | INFO     | 8f9efd7ca93342fd9cb4b55a079eedad | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:27:41.380 | INFO     | 628e1552f7f142e4b7d316ab0bc7e803 | 成功认证Java用户: pythontest
2025-08-13 10:27:41.381 | INFO     | 39ed89e0fb1e45738123572639cbfab3 | 成功认证Java用户: pythontest
2025-08-13 10:27:41.381 | INFO     | 8f9efd7ca93342fd9cb4b55a079eedad | 成功认证Java用户: pythontest
2025-08-13 10:27:41.388 | INFO     | 8f9efd7ca93342fd9cb4b55a079eedad | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:27:41.388 | INFO     | 8f9efd7ca93342fd9cb4b55a079eedad | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:27:41.390 | INFO     | 39ed89e0fb1e45738123572639cbfab3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:27:41.391 | INFO     | 39ed89e0fb1e45738123572639cbfab3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:27:57.166 | INFO     | 8f9efd7ca93342fd9cb4b55a079eedad | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 502 Bad Gateway"
2025-08-13 10:27:57.167 | INFO     | 8f9efd7ca93342fd9cb4b55a079eedad | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 15787.873ms
2025-08-13 10:28:09.517 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:28:14.756 | INFO     | 6f36267240ac4550942bdd0ebdb19e9e | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:28:14.757 | INFO     | 8c2b6b44c93d4ab4a0ae592750a42d47 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:28:14.757 | INFO     | 29487c68272b44e2953d8f16ba9771b9 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:28:14.760 | INFO     | 6f36267240ac4550942bdd0ebdb19e9e | 成功认证Java用户: pythontest
2025-08-13 10:28:14.761 | INFO     | 8c2b6b44c93d4ab4a0ae592750a42d47 | 成功认证Java用户: pythontest
2025-08-13 10:28:14.762 | INFO     | 29487c68272b44e2953d8f16ba9771b9 | 成功认证Java用户: pythontest
2025-08-13 10:28:14.790 | INFO     | 29487c68272b44e2953d8f16ba9771b9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:28:14.790 | INFO     | 29487c68272b44e2953d8f16ba9771b9 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:28:14.797 | INFO     | 8c2b6b44c93d4ab4a0ae592750a42d47 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:28:14.797 | INFO     | 8c2b6b44c93d4ab4a0ae592750a42d47 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:28:39.160 | INFO     | 6f36267240ac4550942bdd0ebdb19e9e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24409.627ms
2025-08-13 10:28:39.164 | INFO     | 29487c68272b44e2953d8f16ba9771b9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24407.050ms
2025-08-13 10:28:39.166 | INFO     | 8c2b6b44c93d4ab4a0ae592750a42d47 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24414.480ms
2025-08-13 10:28:44.048 | INFO     | cc4e2488195744caa52172ea145a11d3 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:28:44.049 | INFO     | ec46393570c94e169df2a9e963e91b60 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:28:44.049 | INFO     | 71d3a5c43a494ece9879663e32d0c7cb | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:28:44.050 | INFO     | cc4e2488195744caa52172ea145a11d3 | 成功认证Java用户: pythontest
2025-08-13 10:28:44.050 | INFO     | ec46393570c94e169df2a9e963e91b60 | 成功认证Java用户: pythontest
2025-08-13 10:28:44.052 | INFO     | 71d3a5c43a494ece9879663e32d0c7cb | 成功认证Java用户: pythontest
2025-08-13 10:28:44.062 | INFO     | 71d3a5c43a494ece9879663e32d0c7cb | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:28:44.064 | INFO     | 71d3a5c43a494ece9879663e32d0c7cb | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:28:44.067 | INFO     | ec46393570c94e169df2a9e963e91b60 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:28:44.067 | INFO     | ec46393570c94e169df2a9e963e91b60 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:29:14.065 | INFO     | cc4e2488195744caa52172ea145a11d3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 30017.556ms
2025-08-13 10:29:14.070 | INFO     | ec46393570c94e169df2a9e963e91b60 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30022.132ms
2025-08-13 10:29:14.073 | INFO     | 71d3a5c43a494ece9879663e32d0c7cb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30024.019ms
2025-08-13 10:31:27.500 | INFO     | dc39e2e22f85449f830fd061b43c00fa | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:31:27.501 | INFO     | 88c1ee022c4c495bbcb6e3606bfd16a0 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:31:27.502 | INFO     | b003e77689fd435faba4f6f308e8db19 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:31:27.504 | INFO     | dc39e2e22f85449f830fd061b43c00fa | 成功认证Java用户: pythontest
2025-08-13 10:31:27.505 | INFO     | 88c1ee022c4c495bbcb6e3606bfd16a0 | 成功认证Java用户: pythontest
2025-08-13 10:31:27.505 | INFO     | b003e77689fd435faba4f6f308e8db19 | 成功认证Java用户: pythontest
2025-08-13 10:31:27.518 | INFO     | b003e77689fd435faba4f6f308e8db19 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:31:27.519 | INFO     | b003e77689fd435faba4f6f308e8db19 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:31:27.523 | INFO     | 88c1ee022c4c495bbcb6e3606bfd16a0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:31:27.523 | INFO     | 88c1ee022c4c495bbcb6e3606bfd16a0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:31:57.531 | INFO     | dc39e2e22f85449f830fd061b43c00fa | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 30030.824ms
2025-08-13 10:31:57.533 | INFO     | b003e77689fd435faba4f6f308e8db19 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30031.352ms
2025-08-13 10:31:57.534 | INFO     | 88c1ee022c4c495bbcb6e3606bfd16a0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30032.512ms
2025-08-13 10:35:33.285 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:35:42.035 | INFO     | db8c240728f3467ea4f169b366063334 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:35:42.037 | INFO     | 36b7006129cc4287a3aff7b17bb679f9 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:35:42.038 | INFO     | 7df99ba278a64c149033fc7e88abcb4c | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:35:42.040 | INFO     | db8c240728f3467ea4f169b366063334 | 成功认证Java用户: pythontest
2025-08-13 10:35:42.043 | INFO     | 36b7006129cc4287a3aff7b17bb679f9 | 成功认证Java用户: pythontest
2025-08-13 10:35:42.044 | INFO     | 7df99ba278a64c149033fc7e88abcb4c | 成功认证Java用户: pythontest
2025-08-13 10:35:42.120 | INFO     | db8c240728f3467ea4f169b366063334 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:35:42.122 | INFO     | 7df99ba278a64c149033fc7e88abcb4c | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:35:42.122 | INFO     | 7df99ba278a64c149033fc7e88abcb4c | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:35:42.125 | INFO     | 36b7006129cc4287a3aff7b17bb679f9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:35:42.127 | INFO     | 36b7006129cc4287a3aff7b17bb679f9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:35:42.134 | INFO     | db8c240728f3467ea4f169b366063334 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 105.425ms
2025-08-13 10:35:42.193 | INFO     | 7df99ba278a64c149033fc7e88abcb4c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:35:42.197 | INFO     | 7df99ba278a64c149033fc7e88abcb4c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 160.599ms
2025-08-13 10:35:42.198 | INFO     | 36b7006129cc4287a3aff7b17bb679f9 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:35:42.199 | INFO     | 36b7006129cc4287a3aff7b17bb679f9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 164.259ms
2025-08-13 10:37:31.841 | INFO     | ed277572fffc431289330de7b9ae77f5 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:37:31.843 | INFO     | ed277572fffc431289330de7b9ae77f5 | 成功认证Java用户: pythontest
2025-08-13 10:37:31.864 | INFO     | ed277572fffc431289330de7b9ae77f5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:37:31.864 | INFO     | ed277572fffc431289330de7b9ae77f5 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-13 10:37:32.075 | INFO     | ed277572fffc431289330de7b9ae77f5 | HTTP Request: PUT http://192.168.66.40:6610/api/v1/datasets/0abbf842774f11f0b5347e63526e5b16 "HTTP/1.1 200 OK"
2025-08-13 10:37:32.079 | INFO     | ed277572fffc431289330de7b9ae77f5 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/0abbf842774f11f0b5347e63526e5b16 | 239.119ms
2025-08-13 10:37:37.952 | INFO     | 789a094c34de456e8acd7c20cf1fde88 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:37:37.955 | INFO     | 789a094c34de456e8acd7c20cf1fde88 | 成功认证Java用户: pythontest
2025-08-13 10:37:37.975 | INFO     | 789a094c34de456e8acd7c20cf1fde88 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:37:37.975 | INFO     | 789a094c34de456e8acd7c20cf1fde88 | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-13 10:37:38.274 | INFO     | 789a094c34de456e8acd7c20cf1fde88 | HTTP Request: DELETE http://192.168.66.40:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-13 10:37:38.276 | INFO     | 789a094c34de456e8acd7c20cf1fde88 | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 323.340ms
2025-08-13 10:37:38.287 | INFO     | 8140ce11054245ec9b07df6e6012a4ab | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:37:38.289 | INFO     | 8140ce11054245ec9b07df6e6012a4ab | 成功认证Java用户: pythontest
2025-08-13 10:37:38.295 | INFO     | 8140ce11054245ec9b07df6e6012a4ab | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:37:38.296 | INFO     | 8140ce11054245ec9b07df6e6012a4ab | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:37:38.311 | INFO     | 8140ce11054245ec9b07df6e6012a4ab | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:37:38.313 | INFO     | 8140ce11054245ec9b07df6e6012a4ab | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.404ms
2025-08-13 10:37:38.315 | INFO     | 6c7598f9735e42eabc1aed52e6171958 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:37:38.317 | INFO     | 6c7598f9735e42eabc1aed52e6171958 | 成功认证Java用户: pythontest
2025-08-13 10:37:38.322 | INFO     | 6c7598f9735e42eabc1aed52e6171958 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:37:38.322 | INFO     | 6c7598f9735e42eabc1aed52e6171958 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:37:38.338 | INFO     | 6c7598f9735e42eabc1aed52e6171958 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:37:38.340 | INFO     | 6c7598f9735e42eabc1aed52e6171958 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.590ms
2025-08-13 10:38:01.426 | INFO     | 34b5a9ad47a6463eb396971926e8054a | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:38:01.427 | INFO     | 34b5a9ad47a6463eb396971926e8054a | 成功认证Java用户: pythontest
2025-08-13 10:38:01.435 | INFO     | 34b5a9ad47a6463eb396971926e8054a | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:38:01.436 | INFO     | 34b5a9ad47a6463eb396971926e8054a | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-13 10:38:01.693 | INFO     | 34b5a9ad47a6463eb396971926e8054a | HTTP Request: POST http://192.168.66.40:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-13 10:38:01.694 | INFO     | 34b5a9ad47a6463eb396971926e8054a | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 267.997ms
2025-08-13 10:38:01.706 | INFO     | 1bde7b5b28304a6988de234f5fbca12f | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:38:01.707 | INFO     | 90101d7d282c4464b84e32c2ea1d0bba | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:38:01.708 | INFO     | 1bde7b5b28304a6988de234f5fbca12f | 成功认证Java用户: pythontest
2025-08-13 10:38:01.709 | INFO     | 90101d7d282c4464b84e32c2ea1d0bba | 成功认证Java用户: pythontest
2025-08-13 10:38:01.716 | INFO     | 1bde7b5b28304a6988de234f5fbca12f | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:38:01.716 | INFO     | 1bde7b5b28304a6988de234f5fbca12f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:38:01.719 | INFO     | 90101d7d282c4464b84e32c2ea1d0bba | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:38:01.719 | INFO     | 90101d7d282c4464b84e32c2ea1d0bba | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:38:01.735 | INFO     | 90101d7d282c4464b84e32c2ea1d0bba | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:38:01.736 | INFO     | 1bde7b5b28304a6988de234f5fbca12f | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:38:01.737 | INFO     | 90101d7d282c4464b84e32c2ea1d0bba | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.246ms
2025-08-13 10:38:01.738 | INFO     | 1bde7b5b28304a6988de234f5fbca12f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 32.612ms
2025-08-13 10:44:30.368 | INFO     | bb7bf17740d14aea81638bb6980f88ea | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:44:30.369 | INFO     | c8bf6a9cf68045c89dec6082e748a11c | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:44:30.373 | INFO     | bb7bf17740d14aea81638bb6980f88ea | 成功认证Java用户: pythontest
2025-08-13 10:44:30.374 | INFO     | c8bf6a9cf68045c89dec6082e748a11c | 成功认证Java用户: pythontest
2025-08-13 10:44:30.375 | INFO     | 6a693be269d04d439422b1e246b42b39 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:44:30.377 | INFO     | 6a693be269d04d439422b1e246b42b39 | 成功认证Java用户: pythontest
2025-08-13 10:44:30.386 | INFO     | bb7bf17740d14aea81638bb6980f88ea | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:44:30.386 | INFO     | bb7bf17740d14aea81638bb6980f88ea | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:44:30.391 | INFO     | c8bf6a9cf68045c89dec6082e748a11c | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:44:30.392 | INFO     | c8bf6a9cf68045c89dec6082e748a11c | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:44:30.397 | INFO     | 6a693be269d04d439422b1e246b42b39 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:44:30.399 | INFO     | 6a693be269d04d439422b1e246b42b39 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 25.449ms
2025-08-13 10:44:30.408 | INFO     | bb7bf17740d14aea81638bb6980f88ea | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:44:30.411 | INFO     | c8bf6a9cf68045c89dec6082e748a11c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:44:30.412 | INFO     | bb7bf17740d14aea81638bb6980f88ea | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 43.903ms
2025-08-13 10:44:30.415 | INFO     | c8bf6a9cf68045c89dec6082e748a11c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 46.231ms
2025-08-13 10:45:44.976 | INFO     | aedbb59f019a4874a957c7932fbcd5ae | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:44.978 | INFO     | 734b9a44127c4364b4fd9e768a5dbf9c | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:44.978 | INFO     | d015554817394189b9c29bfb48390703 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:44.980 | INFO     | 734b9a44127c4364b4fd9e768a5dbf9c | 成功认证Java用户: pythontest
2025-08-13 10:45:44.981 | INFO     | aedbb59f019a4874a957c7932fbcd5ae | 成功认证Java用户: pythontest
2025-08-13 10:45:44.982 | INFO     | d015554817394189b9c29bfb48390703 | 成功认证Java用户: pythontest
2025-08-13 10:45:44.994 | INFO     | 734b9a44127c4364b4fd9e768a5dbf9c | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:45:44.995 | INFO     | 734b9a44127c4364b4fd9e768a5dbf9c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:45:44.999 | INFO     | d015554817394189b9c29bfb48390703 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:45:45.000 | INFO     | d015554817394189b9c29bfb48390703 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:45:45.005 | INFO     | aedbb59f019a4874a957c7932fbcd5ae | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:45:45.007 | INFO     | aedbb59f019a4874a957c7932fbcd5ae | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 31.253ms
2025-08-13 10:45:45.020 | INFO     | d015554817394189b9c29bfb48390703 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:45:45.022 | INFO     | 734b9a44127c4364b4fd9e768a5dbf9c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:45:45.023 | INFO     | d015554817394189b9c29bfb48390703 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 46.003ms
2025-08-13 10:45:45.026 | INFO     | 734b9a44127c4364b4fd9e768a5dbf9c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 48.740ms
2025-08-13 10:45:52.247 | INFO     | 8ec68c8254064304a1e025e2db1f8b9a | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:52.249 | INFO     | 8ec68c8254064304a1e025e2db1f8b9a | 成功认证Java用户: pythontest
2025-08-13 10:45:52.265 | INFO     | 8ec68c8254064304a1e025e2db1f8b9a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:45:52.267 | INFO     | 8ec68c8254064304a1e025e2db1f8b9a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.960ms
2025-08-13 10:45:52.272 | INFO     | b45e7869125347b08d7a87832cb83472 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:52.274 | INFO     | b45e7869125347b08d7a87832cb83472 | 成功认证Java用户: pythontest
2025-08-13 10:45:52.282 | INFO     | b45e7869125347b08d7a87832cb83472 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:45:52.283 | INFO     | b45e7869125347b08d7a87832cb83472 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:45:52.298 | INFO     | b45e7869125347b08d7a87832cb83472 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:45:52.301 | INFO     | b45e7869125347b08d7a87832cb83472 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.555ms
2025-08-13 10:45:52.305 | INFO     | e79adfb14124466ea661b587cf9aaf08 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:52.307 | INFO     | e79adfb14124466ea661b587cf9aaf08 | 成功认证Java用户: pythontest
2025-08-13 10:45:52.314 | INFO     | e79adfb14124466ea661b587cf9aaf08 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:45:52.315 | INFO     | e79adfb14124466ea661b587cf9aaf08 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:45:52.329 | INFO     | e79adfb14124466ea661b587cf9aaf08 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:45:52.331 | INFO     | e79adfb14124466ea661b587cf9aaf08 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.965ms
2025-08-13 10:45:55.555 | INFO     | 69549e622ca94fa08e591a82c91f600e | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:55.556 | INFO     | 66be261a3c3d4e258f7028a6e7b8d5b6 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:55.557 | INFO     | 3b02538bff6e44a9ae5347bebf4b92e2 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:45:55.557 | INFO     | 69549e622ca94fa08e591a82c91f600e | 成功认证Java用户: pythontest
2025-08-13 10:45:55.557 | INFO     | 66be261a3c3d4e258f7028a6e7b8d5b6 | 成功认证Java用户: pythontest
2025-08-13 10:45:55.558 | INFO     | 3b02538bff6e44a9ae5347bebf4b92e2 | 成功认证Java用户: pythontest
2025-08-13 10:45:55.571 | INFO     | 3b02538bff6e44a9ae5347bebf4b92e2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:45:55.572 | INFO     | 3b02538bff6e44a9ae5347bebf4b92e2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:45:55.577 | INFO     | 66be261a3c3d4e258f7028a6e7b8d5b6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:45:55.577 | INFO     | 66be261a3c3d4e258f7028a6e7b8d5b6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:45:55.582 | INFO     | 69549e622ca94fa08e591a82c91f600e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:45:55.585 | INFO     | 69549e622ca94fa08e591a82c91f600e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 30.043ms
2025-08-13 10:45:55.598 | INFO     | 3b02538bff6e44a9ae5347bebf4b92e2 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:45:55.599 | INFO     | 66be261a3c3d4e258f7028a6e7b8d5b6 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:45:55.601 | INFO     | 66be261a3c3d4e258f7028a6e7b8d5b6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 44.781ms
2025-08-13 10:45:55.602 | INFO     | 3b02538bff6e44a9ae5347bebf4b92e2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 45.031ms
2025-08-13 10:46:07.442 | INFO     | bdcad3d7d98f4f74a69eb0858c10c317 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:46:07.443 | INFO     | d9b0cbc4c82a4ed390c806c0cea459b0 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:46:07.443 | INFO     | 5a0e7cec6fef448a857452b3c3e67a7e | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:46:07.444 | INFO     | bdcad3d7d98f4f74a69eb0858c10c317 | 成功认证Java用户: pythontest
2025-08-13 10:46:07.444 | INFO     | d9b0cbc4c82a4ed390c806c0cea459b0 | 成功认证Java用户: pythontest
2025-08-13 10:46:07.445 | INFO     | 5a0e7cec6fef448a857452b3c3e67a7e | 成功认证Java用户: pythontest
2025-08-13 10:46:07.453 | INFO     | 5a0e7cec6fef448a857452b3c3e67a7e | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:46:07.454 | INFO     | 5a0e7cec6fef448a857452b3c3e67a7e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:46:07.457 | INFO     | d9b0cbc4c82a4ed390c806c0cea459b0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:46:07.458 | INFO     | d9b0cbc4c82a4ed390c806c0cea459b0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:46:07.460 | INFO     | bdcad3d7d98f4f74a69eb0858c10c317 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:46:07.463 | INFO     | bdcad3d7d98f4f74a69eb0858c10c317 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.080ms
2025-08-13 10:46:07.473 | INFO     | 5a0e7cec6fef448a857452b3c3e67a7e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:46:07.474 | INFO     | d9b0cbc4c82a4ed390c806c0cea459b0 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:46:07.476 | INFO     | 5a0e7cec6fef448a857452b3c3e67a7e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 32.907ms
2025-08-13 10:46:07.477 | INFO     | d9b0cbc4c82a4ed390c806c0cea459b0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 34.233ms
2025-08-13 10:49:35.880 | INFO     | 1bdce32daaa04c648b3bcd8ecdd00d3f | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:49:35.894 | INFO     | 1bdce32daaa04c648b3bcd8ecdd00d3f | 成功认证Java用户: pythontest
2025-08-13 10:49:35.895 | INFO     | 7f0614681e624607bdf6b8aee28f969b | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:49:35.901 | INFO     | 7f0614681e624607bdf6b8aee28f969b | 成功认证Java用户: pythontest
2025-08-13 10:49:35.907 | INFO     | 7f0614681e624607bdf6b8aee28f969b | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:49:35.907 | INFO     | 7f0614681e624607bdf6b8aee28f969b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:49:35.912 | INFO     | 1bdce32daaa04c648b3bcd8ecdd00d3f | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:49:35.914 | INFO     | 1bdce32daaa04c648b3bcd8ecdd00d3f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 34.555ms
2025-08-13 10:49:35.916 | INFO     | c51f98a0f3504699ac18cc6b5bac93a5 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:49:35.917 | INFO     | c51f98a0f3504699ac18cc6b5bac93a5 | 成功认证Java用户: pythontest
2025-08-13 10:49:35.923 | INFO     | 7f0614681e624607bdf6b8aee28f969b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:49:35.925 | INFO     | 7f0614681e624607bdf6b8aee28f969b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.753ms
2025-08-13 10:49:35.927 | INFO     | c51f98a0f3504699ac18cc6b5bac93a5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:49:35.927 | INFO     | c51f98a0f3504699ac18cc6b5bac93a5 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:49:35.940 | INFO     | c51f98a0f3504699ac18cc6b5bac93a5 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:49:35.941 | INFO     | c51f98a0f3504699ac18cc6b5bac93a5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.329ms
2025-08-13 10:51:23.175 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:51:23.369 | INFO     | - | Started server process [32460]
2025-08-13 10:51:23.370 | INFO     | - | Waiting for application startup.
2025-08-13 10:51:23.487 | INFO     | - | Application startup complete.
2025-08-13 10:51:43.078 | INFO     | - | Shutting down
2025-08-13 10:51:43.187 | INFO     | - | Waiting for application shutdown.
2025-08-13 10:51:43.187 | INFO     | - | Application shutdown complete.
2025-08-13 10:51:43.187 | INFO     | - | Finished server process [32460]
2025-08-13 10:52:03.448 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:52:07.774 | INFO     | db71874ba2e04b2b9c33f771d494cf16 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:52:07.776 | INFO     | 1cd4516e3ced47809a6aed943c6a6079 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:52:07.777 | INFO     | 2de037cee7a34b2ea46653854daf7743 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:52:07.779 | INFO     | db71874ba2e04b2b9c33f771d494cf16 | 成功认证Java用户: pythontest
2025-08-13 10:52:07.782 | INFO     | 1cd4516e3ced47809a6aed943c6a6079 | 成功认证Java用户: pythontest
2025-08-13 10:52:07.782 | INFO     | 2de037cee7a34b2ea46653854daf7743 | 成功认证Java用户: pythontest
2025-08-13 10:52:07.819 | INFO     | db71874ba2e04b2b9c33f771d494cf16 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:52:07.822 | INFO     | db71874ba2e04b2b9c33f771d494cf16 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 52.729ms
2025-08-13 10:52:07.826 | INFO     | 1cd4516e3ced47809a6aed943c6a6079 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:52:07.827 | INFO     | 1cd4516e3ced47809a6aed943c6a6079 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:52:07.831 | INFO     | 2de037cee7a34b2ea46653854daf7743 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:52:07.831 | INFO     | 2de037cee7a34b2ea46653854daf7743 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:52:07.849 | INFO     | 1cd4516e3ced47809a6aed943c6a6079 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:52:07.851 | INFO     | 2de037cee7a34b2ea46653854daf7743 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:52:07.853 | INFO     | 1cd4516e3ced47809a6aed943c6a6079 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 77.605ms
2025-08-13 10:52:07.854 | INFO     | 2de037cee7a34b2ea46653854daf7743 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 77.992ms
2025-08-13 10:54:49.963 | INFO     | b8331907be77408ea64abccc4d633406 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:54:49.963 | INFO     | a5b5a0cf749f424b92e4c3bec11f67fb | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:54:49.965 | INFO     | b8331907be77408ea64abccc4d633406 | 成功认证Java用户: pythontest
2025-08-13 10:54:49.965 | INFO     | a5b5a0cf749f424b92e4c3bec11f67fb | 成功认证Java用户: pythontest
2025-08-13 10:54:49.974 | INFO     | a5b5a0cf749f424b92e4c3bec11f67fb | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:54:49.974 | INFO     | a5b5a0cf749f424b92e4c3bec11f67fb | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 10:54:49.977 | INFO     | b8331907be77408ea64abccc4d633406 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 10:54:49.977 | INFO     | b8331907be77408ea64abccc4d633406 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 10:54:49.992 | INFO     | a5b5a0cf749f424b92e4c3bec11f67fb | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:54:49.993 | INFO     | b8331907be77408ea64abccc4d633406 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 10:54:49.993 | INFO     | a5b5a0cf749f424b92e4c3bec11f67fb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.482ms
2025-08-13 10:54:49.994 | INFO     | b8331907be77408ea64abccc4d633406 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 32.550ms
2025-08-13 10:54:49.995 | INFO     | 0f18a6f4fdda4c158270b4efaf146460 | JWT标准验证成功，获取UUID: 256f2c6b-1535-47bd-9744-f28a67a5daa0
2025-08-13 10:54:49.996 | INFO     | 0f18a6f4fdda4c158270b4efaf146460 | 成功认证Java用户: pythontest
2025-08-13 10:54:50.006 | INFO     | 0f18a6f4fdda4c158270b4efaf146460 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 10:54:50.006 | INFO     | 0f18a6f4fdda4c158270b4efaf146460 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 11.472ms
2025-08-13 10:56:46.915 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:56:47.077 | INFO     | - | Started server process [35456]
2025-08-13 10:56:47.077 | INFO     | - | Waiting for application startup.
2025-08-13 10:56:47.155 | INFO     | - | Application startup complete.
2025-08-13 10:57:11.876 | INFO     | efd15087db8b4ebf986f211098d319c3 | 127.0.0.1       | GET      | 404    | / | 2.701ms
2025-08-13 10:57:11.915 | INFO     | 48bf113445dc41469f737cdf86fde72a | 127.0.0.1       | GET      | 404    | /.well-known/appspecific/com.chrome.devtools.json | 1.961ms
2025-08-13 10:57:11.951 | INFO     | 2d6c022a20b04ea9bd5fb081ed9b955f | 127.0.0.1       | GET      | 404    | /favicon.ico | 5.804ms
2025-08-13 10:59:39.897 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 10:59:40.065 | INFO     | - | Started server process [31340]
2025-08-13 10:59:40.065 | INFO     | - | Waiting for application startup.
2025-08-13 10:59:40.176 | INFO     | - | Application startup complete.
2025-08-13 11:00:18.804 | INFO     | c437b6bfd54a4ccfa54afd01a80753d1 | 127.0.0.1       | GET      | 401    | /api/iot/v1/knowledge-base/models/embedding | 3.598ms
2025-08-13 11:06:17.462 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 11:07:01.630 | INFO     | e9d0cdf3cd724d85ae967408e7546986 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:07:01.635 | INFO     | e9d0cdf3cd724d85ae967408e7546986 | 成功认证Java用户: pythontest
2025-08-13 11:07:01.665 | INFO     | e9d0cdf3cd724d85ae967408e7546986 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:07:01.667 | INFO     | e9d0cdf3cd724d85ae967408e7546986 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 49.003ms
2025-08-13 11:07:01.669 | INFO     | 1a9408777178402d93f4cb6d1b463050 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:07:01.670 | INFO     | 1a9408777178402d93f4cb6d1b463050 | 成功认证Java用户: pythontest
2025-08-13 11:07:01.683 | INFO     | 1a9408777178402d93f4cb6d1b463050 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:07:01.684 | INFO     | 1a9408777178402d93f4cb6d1b463050 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:07:01.697 | INFO     | 1a9408777178402d93f4cb6d1b463050 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:07:01.698 | INFO     | 1a9408777178402d93f4cb6d1b463050 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.454ms
2025-08-13 11:07:01.701 | INFO     | 1321960613aa4b5ebf001346a1b752e1 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:07:01.701 | INFO     | 1321960613aa4b5ebf001346a1b752e1 | 成功认证Java用户: pythontest
2025-08-13 11:07:01.716 | INFO     | 1321960613aa4b5ebf001346a1b752e1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:07:01.717 | INFO     | 1321960613aa4b5ebf001346a1b752e1 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:07:01.730 | INFO     | 1321960613aa4b5ebf001346a1b752e1 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:07:01.732 | INFO     | 1321960613aa4b5ebf001346a1b752e1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.977ms
2025-08-13 11:07:08.689 | INFO     | 9731005dedda4ebda16f14664eca46c3 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:07:08.690 | INFO     | a17ed25597f54f4fa37740d758b89b6a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:07:08.691 | INFO     | 9731005dedda4ebda16f14664eca46c3 | 成功认证Java用户: pythontest
2025-08-13 11:07:08.692 | INFO     | b0d454792d064cf084d515498a0e82df | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:07:08.695 | INFO     | a17ed25597f54f4fa37740d758b89b6a | 成功认证Java用户: pythontest
2025-08-13 11:07:08.703 | INFO     | b0d454792d064cf084d515498a0e82df | 成功认证Java用户: pythontest
2025-08-13 11:07:08.709 | INFO     | a17ed25597f54f4fa37740d758b89b6a | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:07:08.710 | INFO     | a17ed25597f54f4fa37740d758b89b6a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:07:08.719 | INFO     | b0d454792d064cf084d515498a0e82df | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:07:08.720 | INFO     | b0d454792d064cf084d515498a0e82df | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:07:08.809 | INFO     | a17ed25597f54f4fa37740d758b89b6a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:07:08.811 | INFO     | b0d454792d064cf084d515498a0e82df | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:07:08.812 | INFO     | 9731005dedda4ebda16f14664eca46c3 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:07:08.813 | INFO     | a17ed25597f54f4fa37740d758b89b6a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 123.201ms
2025-08-13 11:07:08.815 | INFO     | b0d454792d064cf084d515498a0e82df | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 122.731ms
2025-08-13 11:07:08.815 | INFO     | 9731005dedda4ebda16f14664eca46c3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 126.791ms
2025-08-13 11:11:06.123 | INFO     | 4543968788d6445f8ec60b136867bfd8 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:11:06.124 | INFO     | e3a8d3b164124378adf7269c32dc6a02 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:11:06.125 | INFO     | 5c65dff6d0c541ed97c287e2eb56501d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:11:06.126 | INFO     | 4543968788d6445f8ec60b136867bfd8 | 成功认证Java用户: pythontest
2025-08-13 11:11:06.126 | INFO     | e3a8d3b164124378adf7269c32dc6a02 | 成功认证Java用户: pythontest
2025-08-13 11:11:06.127 | INFO     | 5c65dff6d0c541ed97c287e2eb56501d | 成功认证Java用户: pythontest
2025-08-13 11:11:06.134 | INFO     | 5c65dff6d0c541ed97c287e2eb56501d | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:11:06.134 | INFO     | 5c65dff6d0c541ed97c287e2eb56501d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:11:06.137 | INFO     | e3a8d3b164124378adf7269c32dc6a02 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:11:06.137 | INFO     | e3a8d3b164124378adf7269c32dc6a02 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:11:06.141 | INFO     | 4543968788d6445f8ec60b136867bfd8 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:11:06.141 | INFO     | 4543968788d6445f8ec60b136867bfd8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.741ms
2025-08-13 11:11:06.150 | INFO     | 5c65dff6d0c541ed97c287e2eb56501d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:11:06.152 | INFO     | e3a8d3b164124378adf7269c32dc6a02 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:11:06.152 | INFO     | 5c65dff6d0c541ed97c287e2eb56501d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 28.678ms
2025-08-13 11:11:06.153 | INFO     | e3a8d3b164124378adf7269c32dc6a02 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.428ms
2025-08-13 11:11:36.823 | INFO     | ea9e3fbcd0a34f648c4156995cbd2c2d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:11:36.824 | INFO     | ea9e3fbcd0a34f648c4156995cbd2c2d | 成功认证Java用户: pythontest
2025-08-13 11:11:36.835 | INFO     | ea9e3fbcd0a34f648c4156995cbd2c2d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:11:36.836 | INFO     | ea9e3fbcd0a34f648c4156995cbd2c2d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 13.536ms
2025-08-13 11:11:36.837 | INFO     | a6e40912d34244d2aafaf6669f4810d7 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:11:36.839 | INFO     | a6e40912d34244d2aafaf6669f4810d7 | 成功认证Java用户: pythontest
2025-08-13 11:11:36.843 | INFO     | a6e40912d34244d2aafaf6669f4810d7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:11:36.843 | INFO     | a6e40912d34244d2aafaf6669f4810d7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:11:36.854 | INFO     | a6e40912d34244d2aafaf6669f4810d7 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:11:36.855 | INFO     | a6e40912d34244d2aafaf6669f4810d7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 17.149ms
2025-08-13 11:11:36.857 | INFO     | 5e2c6da08c954f908bc459e394846fd0 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:11:36.858 | INFO     | 5e2c6da08c954f908bc459e394846fd0 | 成功认证Java用户: pythontest
2025-08-13 11:11:36.863 | INFO     | 5e2c6da08c954f908bc459e394846fd0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:11:36.863 | INFO     | 5e2c6da08c954f908bc459e394846fd0 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:11:36.873 | INFO     | 5e2c6da08c954f908bc459e394846fd0 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:11:36.874 | INFO     | 5e2c6da08c954f908bc459e394846fd0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 16.964ms
2025-08-13 11:16:24.013 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 11:17:15.285 | INFO     | ff5bc7f01ab9437ba668981bcc268176 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:17:15.287 | INFO     | ca778012130c4f579d47abfd13088a4b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:17:15.289 | INFO     | ff5bc7f01ab9437ba668981bcc268176 | 成功认证Java用户: pythontest
2025-08-13 11:17:15.291 | INFO     | ca778012130c4f579d47abfd13088a4b | 成功认证Java用户: pythontest
2025-08-13 11:17:15.321 | INFO     | ff5bc7f01ab9437ba668981bcc268176 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:17:15.323 | INFO     | ff5bc7f01ab9437ba668981bcc268176 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 43.726ms
2025-08-13 11:17:15.326 | INFO     | bbade5a913dc41218f21082d24a7b918 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:17:15.329 | INFO     | bbade5a913dc41218f21082d24a7b918 | 成功认证Java用户: pythontest
2025-08-13 11:17:15.332 | INFO     | ca778012130c4f579d47abfd13088a4b | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:17:15.333 | INFO     | ca778012130c4f579d47abfd13088a4b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:17:15.343 | INFO     | bbade5a913dc41218f21082d24a7b918 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:17:15.344 | INFO     | bbade5a913dc41218f21082d24a7b918 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:17:15.349 | INFO     | ca778012130c4f579d47abfd13088a4b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:17:15.351 | INFO     | ca778012130c4f579d47abfd13088a4b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 64.389ms
2025-08-13 11:17:15.368 | INFO     | bbade5a913dc41218f21082d24a7b918 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:17:15.370 | INFO     | bbade5a913dc41218f21082d24a7b918 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 43.940ms
2025-08-13 11:17:57.154 | INFO     | a29ac6beafa844918b77afa328aff27f | 127.0.0.1       | GET      | 401    | /api/iot/v1/knowledge-base/models/embedding | 0.460ms
2025-08-13 11:18:08.058 | INFO     | d81ff4b3eba24af584e5f5f72590d169 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:18:08.058 | INFO     | f6db9f708aaf4c5fae4dc25340ff514e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:18:08.059 | INFO     | 48de31c32c76455b843ee15ebe0cfc38 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:18:08.061 | INFO     | d81ff4b3eba24af584e5f5f72590d169 | 成功认证Java用户: pythontest
2025-08-13 11:18:08.062 | INFO     | f6db9f708aaf4c5fae4dc25340ff514e | 成功认证Java用户: pythontest
2025-08-13 11:18:08.064 | INFO     | 48de31c32c76455b843ee15ebe0cfc38 | 成功认证Java用户: pythontest
2025-08-13 11:18:08.084 | INFO     | 48de31c32c76455b843ee15ebe0cfc38 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:18:08.085 | INFO     | 48de31c32c76455b843ee15ebe0cfc38 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:18:08.090 | INFO     | d81ff4b3eba24af584e5f5f72590d169 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:18:08.092 | INFO     | f6db9f708aaf4c5fae4dc25340ff514e | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:18:08.093 | INFO     | f6db9f708aaf4c5fae4dc25340ff514e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:18:08.097 | INFO     | d81ff4b3eba24af584e5f5f72590d169 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 39.584ms
2025-08-13 11:18:08.108 | INFO     | 48de31c32c76455b843ee15ebe0cfc38 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:18:08.110 | INFO     | f6db9f708aaf4c5fae4dc25340ff514e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:18:08.112 | INFO     | 48de31c32c76455b843ee15ebe0cfc38 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 52.861ms
2025-08-13 11:18:08.113 | INFO     | f6db9f708aaf4c5fae4dc25340ff514e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 54.671ms
2025-08-13 11:18:50.671 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 11:19:05.790 | INFO     | 432f24463d1a4345ae980d8d3a0148c5 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:19:05.792 | INFO     | 432f24463d1a4345ae980d8d3a0148c5 | 成功认证Java用户: pythontest
2025-08-13 11:19:05.802 | INFO     | 432f24463d1a4345ae980d8d3a0148c5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:19:05.803 | INFO     | 432f24463d1a4345ae980d8d3a0148c5 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-13 11:19:05.989 | INFO     | 432f24463d1a4345ae980d8d3a0148c5 | HTTP Request: GET http://192.168.66.40:6610/api/v1/llm/list?model_type=embedding "HTTP/1.1 200 OK"
2025-08-13 11:19:05.990 | INFO     | 432f24463d1a4345ae980d8d3a0148c5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/models/embedding | 202.559ms
2025-08-13 11:19:46.989 | INFO     | c9296128bbc44f94b4ddf45cff4a1368 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:19:46.990 | INFO     | c9296128bbc44f94b4ddf45cff4a1368 | 成功认证Java用户: pythontest
2025-08-13 11:19:46.999 | INFO     | c9296128bbc44f94b4ddf45cff4a1368 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:19:47.000 | INFO     | c9296128bbc44f94b4ddf45cff4a1368 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-13 11:19:47.007 | INFO     | c9296128bbc44f94b4ddf45cff4a1368 | HTTP Request: GET http://192.168.66.40:6610/api/v1/llm/list?model_type=embedding "HTTP/1.1 200 OK"
2025-08-13 11:19:47.008 | INFO     | c9296128bbc44f94b4ddf45cff4a1368 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/models/embedding | 19.463ms
2025-08-13 11:20:03.655 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-13 11:20:31.388 | INFO     | 20e0c00a09c945efb1061f5c52b1423c | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:20:31.390 | INFO     | 20e0c00a09c945efb1061f5c52b1423c | 成功认证Java用户: pythontest
2025-08-13 11:20:31.403 | INFO     | 20e0c00a09c945efb1061f5c52b1423c | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:20:31.404 | INFO     | 20e0c00a09c945efb1061f5c52b1423c | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-13 11:20:31.419 | INFO     | 20e0c00a09c945efb1061f5c52b1423c | HTTP Request: GET http://192.168.66.40:6610/api/v1/llm/list?model_type=embedding "HTTP/1.1 200 OK"
2025-08-13 11:20:31.420 | INFO     | 20e0c00a09c945efb1061f5c52b1423c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/models/embedding | 34.704ms
2025-08-13 11:21:04.965 | INFO     | 6c97a4c6e6ae4e6081a3b09053029625 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:04.965 | INFO     | 034155d81358403a8e2f6b5b6621fdcd | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:04.967 | INFO     | 6c97a4c6e6ae4e6081a3b09053029625 | 成功认证Java用户: pythontest
2025-08-13 11:21:04.969 | INFO     | 034155d81358403a8e2f6b5b6621fdcd | 成功认证Java用户: pythontest
2025-08-13 11:21:04.986 | INFO     | 6c97a4c6e6ae4e6081a3b09053029625 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:21:04.989 | INFO     | 6c97a4c6e6ae4e6081a3b09053029625 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 25.667ms
2025-08-13 11:21:04.993 | INFO     | 4fce828894dc46799e1378cbf786948b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:04.994 | INFO     | 4fce828894dc46799e1378cbf786948b | 成功认证Java用户: pythontest
2025-08-13 11:21:04.996 | INFO     | 034155d81358403a8e2f6b5b6621fdcd | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:04.996 | INFO     | 034155d81358403a8e2f6b5b6621fdcd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:21:05.009 | INFO     | 4fce828894dc46799e1378cbf786948b | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:05.009 | INFO     | 4fce828894dc46799e1378cbf786948b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:21:05.012 | INFO     | 034155d81358403a8e2f6b5b6621fdcd | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:05.013 | INFO     | 034155d81358403a8e2f6b5b6621fdcd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 48.076ms
2025-08-13 11:21:05.021 | INFO     | 4fce828894dc46799e1378cbf786948b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:05.023 | INFO     | 4fce828894dc46799e1378cbf786948b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.019ms
2025-08-13 11:21:08.203 | INFO     | 8fed84362b7843c7900d4320027bed71 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:08.204 | INFO     | fb8678687ca84e708e7b06a4ad725b15 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:08.206 | INFO     | 8fed84362b7843c7900d4320027bed71 | 成功认证Java用户: pythontest
2025-08-13 11:21:08.206 | INFO     | fb8678687ca84e708e7b06a4ad725b15 | 成功认证Java用户: pythontest
2025-08-13 11:21:08.207 | INFO     | 22f384511a6f49ffb6fcbefe24a92d10 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:08.215 | INFO     | 22f384511a6f49ffb6fcbefe24a92d10 | 成功认证Java用户: pythontest
2025-08-13 11:21:08.223 | INFO     | fb8678687ca84e708e7b06a4ad725b15 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:08.224 | INFO     | fb8678687ca84e708e7b06a4ad725b15 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:21:08.228 | INFO     | 22f384511a6f49ffb6fcbefe24a92d10 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:08.228 | INFO     | 22f384511a6f49ffb6fcbefe24a92d10 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:21:08.231 | INFO     | 8fed84362b7843c7900d4320027bed71 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:21:08.236 | INFO     | 8fed84362b7843c7900d4320027bed71 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 32.986ms
2025-08-13 11:21:08.249 | INFO     | fb8678687ca84e708e7b06a4ad725b15 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:08.251 | INFO     | 22f384511a6f49ffb6fcbefe24a92d10 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:08.253 | INFO     | fb8678687ca84e708e7b06a4ad725b15 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 49.021ms
2025-08-13 11:21:08.255 | INFO     | 22f384511a6f49ffb6fcbefe24a92d10 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 47.193ms
2025-08-13 11:21:33.678 | INFO     | 19dfdc1890e7443082540b5ba84ea15a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:33.679 | INFO     | 19dfdc1890e7443082540b5ba84ea15a | 成功认证Java用户: pythontest
2025-08-13 11:21:33.690 | INFO     | 19dfdc1890e7443082540b5ba84ea15a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:21:33.692 | INFO     | 19dfdc1890e7443082540b5ba84ea15a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 14.782ms
2025-08-13 11:21:33.694 | INFO     | 7524801f301f42b1bc85a42700424c24 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:33.695 | INFO     | 7524801f301f42b1bc85a42700424c24 | 成功认证Java用户: pythontest
2025-08-13 11:21:33.700 | INFO     | 7524801f301f42b1bc85a42700424c24 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:33.700 | INFO     | 7524801f301f42b1bc85a42700424c24 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:21:33.713 | INFO     | 7524801f301f42b1bc85a42700424c24 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:33.715 | INFO     | 7524801f301f42b1bc85a42700424c24 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 20.814ms
2025-08-13 11:21:33.718 | INFO     | 7e42a48544f94bb89a64e64806c95a99 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:33.719 | INFO     | 7e42a48544f94bb89a64e64806c95a99 | 成功认证Java用户: pythontest
2025-08-13 11:21:33.723 | INFO     | 7e42a48544f94bb89a64e64806c95a99 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:33.723 | INFO     | 7e42a48544f94bb89a64e64806c95a99 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:21:33.735 | INFO     | 7e42a48544f94bb89a64e64806c95a99 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:33.737 | INFO     | 7e42a48544f94bb89a64e64806c95a99 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 19.327ms
2025-08-13 11:21:39.757 | INFO     | 194a201f59934d779875e4db68931f7a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:39.758 | INFO     | 9f206ed8bcae49e4918739f2b9604dda | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:39.758 | INFO     | 194a201f59934d779875e4db68931f7a | 成功认证Java用户: pythontest
2025-08-13 11:21:39.763 | INFO     | 9f206ed8bcae49e4918739f2b9604dda | 成功认证Java用户: pythontest
2025-08-13 11:21:39.774 | INFO     | 9f206ed8bcae49e4918739f2b9604dda | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:39.774 | INFO     | 9f206ed8bcae49e4918739f2b9604dda | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:21:39.778 | INFO     | 194a201f59934d779875e4db68931f7a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:21:39.781 | INFO     | 194a201f59934d779875e4db68931f7a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.170ms
2025-08-13 11:21:39.785 | INFO     | a90ad6054882483cacd0fa29e59446e7 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:21:39.787 | INFO     | a90ad6054882483cacd0fa29e59446e7 | 成功认证Java用户: pythontest
2025-08-13 11:21:39.790 | INFO     | 9f206ed8bcae49e4918739f2b9604dda | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:39.792 | INFO     | 9f206ed8bcae49e4918739f2b9604dda | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 33.706ms
2025-08-13 11:21:39.796 | INFO     | a90ad6054882483cacd0fa29e59446e7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:21:39.798 | INFO     | a90ad6054882483cacd0fa29e59446e7 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:21:39.811 | INFO     | a90ad6054882483cacd0fa29e59446e7 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:21:39.813 | INFO     | a90ad6054882483cacd0fa29e59446e7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 27.819ms
2025-08-13 11:22:29.012 | INFO     | 0d317b0d2e28462f9f060ccdee142e7e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:29.013 | INFO     | 0d317b0d2e28462f9f060ccdee142e7e | 成功认证Java用户: pythontest
2025-08-13 11:22:29.024 | INFO     | 0d317b0d2e28462f9f060ccdee142e7e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:22:29.025 | INFO     | 0d317b0d2e28462f9f060ccdee142e7e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 12.985ms
2025-08-13 11:22:29.026 | INFO     | ce31c13433864f0ba2f10d16f60e21ab | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:29.027 | INFO     | ce31c13433864f0ba2f10d16f60e21ab | 成功认证Java用户: pythontest
2025-08-13 11:22:29.031 | INFO     | ce31c13433864f0ba2f10d16f60e21ab | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:22:29.031 | INFO     | ce31c13433864f0ba2f10d16f60e21ab | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:22:29.042 | INFO     | ce31c13433864f0ba2f10d16f60e21ab | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:22:29.043 | INFO     | ce31c13433864f0ba2f10d16f60e21ab | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 16.800ms
2025-08-13 11:22:29.046 | INFO     | 6b3a871bd6ce4e41b3362a4bc522b252 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:29.047 | INFO     | 6b3a871bd6ce4e41b3362a4bc522b252 | 成功认证Java用户: pythontest
2025-08-13 11:22:29.051 | INFO     | 6b3a871bd6ce4e41b3362a4bc522b252 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:22:29.051 | INFO     | 6b3a871bd6ce4e41b3362a4bc522b252 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:22:29.061 | INFO     | 6b3a871bd6ce4e41b3362a4bc522b252 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:22:29.062 | INFO     | 6b3a871bd6ce4e41b3362a4bc522b252 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 15.638ms
2025-08-13 11:22:29.064 | INFO     | bed3e3b09910452e84a7101abd63e59f | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:29.065 | INFO     | bed3e3b09910452e84a7101abd63e59f | 成功认证Java用户: pythontest
2025-08-13 11:22:29.068 | INFO     | bed3e3b09910452e84a7101abd63e59f | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:22:29.068 | INFO     | bed3e3b09910452e84a7101abd63e59f | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-13 11:22:29.075 | INFO     | bed3e3b09910452e84a7101abd63e59f | HTTP Request: GET http://192.168.66.40:6610/api/v1/llm/list?model_type=embedding "HTTP/1.1 200 OK"
2025-08-13 11:22:29.076 | INFO     | bed3e3b09910452e84a7101abd63e59f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/models/embedding | 11.819ms
2025-08-13 11:22:46.133 | INFO     | d156df698f7e4072871e423e1751ce0a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:46.134 | INFO     | 486c7a581bab4c36ad2bf1429669c96d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:46.135 | INFO     | 1fde4b15ee634d799d39ccc0b1990364 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:46.135 | INFO     | 5026616e832c425facc35ca58297db54 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:22:46.136 | INFO     | d156df698f7e4072871e423e1751ce0a | 成功认证Java用户: pythontest
2025-08-13 11:22:46.139 | INFO     | 1fde4b15ee634d799d39ccc0b1990364 | 成功认证Java用户: pythontest
2025-08-13 11:22:46.141 | INFO     | 486c7a581bab4c36ad2bf1429669c96d | 成功认证Java用户: pythontest
2025-08-13 11:22:46.143 | INFO     | 5026616e832c425facc35ca58297db54 | 成功认证Java用户: pythontest
2025-08-13 11:22:46.153 | INFO     | 1fde4b15ee634d799d39ccc0b1990364 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:22:46.154 | INFO     | 1fde4b15ee634d799d39ccc0b1990364 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:22:46.161 | INFO     | d156df698f7e4072871e423e1751ce0a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:22:46.165 | INFO     | d156df698f7e4072871e423e1751ce0a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 32.026ms
2025-08-13 11:22:46.166 | INFO     | 5026616e832c425facc35ca58297db54 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:22:46.167 | INFO     | 5026616e832c425facc35ca58297db54 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-13 11:22:46.172 | INFO     | 1fde4b15ee634d799d39ccc0b1990364 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:22:46.176 | INFO     | 1fde4b15ee634d799d39ccc0b1990364 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 41.259ms
2025-08-13 11:22:46.181 | INFO     | 486c7a581bab4c36ad2bf1429669c96d | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:22:46.181 | INFO     | 486c7a581bab4c36ad2bf1429669c96d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:22:46.185 | INFO     | 5026616e832c425facc35ca58297db54 | HTTP Request: GET http://192.168.66.40:6610/api/v1/llm/list?model_type=embedding "HTTP/1.1 200 OK"
2025-08-13 11:22:46.187 | INFO     | 5026616e832c425facc35ca58297db54 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/models/embedding | 52.837ms
2025-08-13 11:22:46.197 | INFO     | 486c7a581bab4c36ad2bf1429669c96d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:22:46.198 | INFO     | 486c7a581bab4c36ad2bf1429669c96d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 64.560ms
2025-08-13 11:33:08.387 | INFO     | 0a63146f12af4f1aa798d450fc51ffb7 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:08.389 | INFO     | 0a63146f12af4f1aa798d450fc51ffb7 | 成功认证Java用户: pythontest
2025-08-13 11:33:08.402 | INFO     | 0a63146f12af4f1aa798d450fc51ffb7 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:33:08.404 | INFO     | 0a63146f12af4f1aa798d450fc51ffb7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.124ms
2025-08-13 11:33:08.406 | INFO     | 000f0927132e471d95bbed848cec79d0 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:08.407 | INFO     | 000f0927132e471d95bbed848cec79d0 | 成功认证Java用户: pythontest
2025-08-13 11:33:08.412 | INFO     | 000f0927132e471d95bbed848cec79d0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:08.413 | INFO     | 000f0927132e471d95bbed848cec79d0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:33:08.425 | INFO     | 000f0927132e471d95bbed848cec79d0 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:08.426 | INFO     | 000f0927132e471d95bbed848cec79d0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 20.310ms
2025-08-13 11:33:08.427 | INFO     | ba717645001f491582f9147dd83dae0a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:08.428 | INFO     | ba717645001f491582f9147dd83dae0a | 成功认证Java用户: pythontest
2025-08-13 11:33:08.433 | INFO     | ba717645001f491582f9147dd83dae0a | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:08.434 | INFO     | ba717645001f491582f9147dd83dae0a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:33:08.446 | INFO     | ba717645001f491582f9147dd83dae0a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:08.447 | INFO     | ba717645001f491582f9147dd83dae0a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 20.231ms
2025-08-13 11:33:08.449 | INFO     | 7496179967a5480ebe4ec374e14b928f | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:08.450 | INFO     | 7496179967a5480ebe4ec374e14b928f | 成功认证Java用户: pythontest
2025-08-13 11:33:08.455 | INFO     | 7496179967a5480ebe4ec374e14b928f | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:08.456 | INFO     | 7496179967a5480ebe4ec374e14b928f | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-13 11:33:08.464 | INFO     | 7496179967a5480ebe4ec374e14b928f | HTTP Request: GET http://192.168.66.40:6610/api/v1/llm/list?model_type=embedding "HTTP/1.1 200 OK"
2025-08-13 11:33:08.466 | INFO     | 7496179967a5480ebe4ec374e14b928f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/models/embedding | 16.286ms
2025-08-13 11:33:21.145 | INFO     | 43d0179164294bc0bfccf2fde6ed3588 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:21.146 | INFO     | d1365db9169a40d1a5385a681fc59635 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:21.146 | INFO     | 522835788c4d445e8b2f8dd96de9b3ab | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:21.147 | INFO     | 43d0179164294bc0bfccf2fde6ed3588 | 成功认证Java用户: pythontest
2025-08-13 11:33:21.148 | INFO     | d1365db9169a40d1a5385a681fc59635 | 成功认证Java用户: pythontest
2025-08-13 11:33:21.149 | INFO     | 522835788c4d445e8b2f8dd96de9b3ab | 成功认证Java用户: pythontest
2025-08-13 11:33:21.154 | INFO     | d1365db9169a40d1a5385a681fc59635 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:21.155 | INFO     | d1365db9169a40d1a5385a681fc59635 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:33:21.158 | INFO     | 522835788c4d445e8b2f8dd96de9b3ab | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:21.159 | INFO     | 522835788c4d445e8b2f8dd96de9b3ab | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:33:21.161 | INFO     | 43d0179164294bc0bfccf2fde6ed3588 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:33:21.164 | INFO     | 43d0179164294bc0bfccf2fde6ed3588 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.781ms
2025-08-13 11:33:21.176 | INFO     | d1365db9169a40d1a5385a681fc59635 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:21.177 | INFO     | 522835788c4d445e8b2f8dd96de9b3ab | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:21.179 | INFO     | d1365db9169a40d1a5385a681fc59635 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 33.336ms
2025-08-13 11:33:21.180 | INFO     | 522835788c4d445e8b2f8dd96de9b3ab | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.102ms
2025-08-13 11:33:31.536 | INFO     | b2dbec051c684f8d8c8d18b27938972e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:31.537 | INFO     | 4d5f089772e04174a66a4989793983ae | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:31.538 | INFO     | b2dbec051c684f8d8c8d18b27938972e | 成功认证Java用户: pythontest
2025-08-13 11:33:31.539 | INFO     | 4d5f089772e04174a66a4989793983ae | 成功认证Java用户: pythontest
2025-08-13 11:33:31.551 | INFO     | 4d5f089772e04174a66a4989793983ae | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:31.552 | INFO     | 4d5f089772e04174a66a4989793983ae | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:33:31.556 | INFO     | b2dbec051c684f8d8c8d18b27938972e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:33:31.558 | INFO     | b2dbec051c684f8d8c8d18b27938972e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.767ms
2025-08-13 11:33:31.560 | INFO     | 60ba25021e9942d79e5ab306be9e60f0 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:31.561 | INFO     | 60ba25021e9942d79e5ab306be9e60f0 | 成功认证Java用户: pythontest
2025-08-13 11:33:31.567 | INFO     | 4d5f089772e04174a66a4989793983ae | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:31.567 | INFO     | 60ba25021e9942d79e5ab306be9e60f0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:31.568 | INFO     | 60ba25021e9942d79e5ab306be9e60f0 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:33:31.573 | INFO     | 4d5f089772e04174a66a4989793983ae | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 36.488ms
2025-08-13 11:33:31.585 | INFO     | 60ba25021e9942d79e5ab306be9e60f0 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:31.586 | INFO     | 60ba25021e9942d79e5ab306be9e60f0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 26.570ms
2025-08-13 11:33:39.483 | INFO     | 05eb337e08064c7db09c62143c578130 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:39.485 | INFO     | 05eb337e08064c7db09c62143c578130 | 成功认证Java用户: pythontest
2025-08-13 11:33:39.499 | INFO     | 05eb337e08064c7db09c62143c578130 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:33:39.500 | INFO     | 05eb337e08064c7db09c62143c578130 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.820ms
2025-08-13 11:33:39.503 | INFO     | eff1ac0e1eb44f9c820726d9db8c1198 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:39.504 | INFO     | eff1ac0e1eb44f9c820726d9db8c1198 | 成功认证Java用户: pythontest
2025-08-13 11:33:39.510 | INFO     | eff1ac0e1eb44f9c820726d9db8c1198 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:39.510 | INFO     | eff1ac0e1eb44f9c820726d9db8c1198 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:33:39.524 | INFO     | eff1ac0e1eb44f9c820726d9db8c1198 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:39.525 | INFO     | eff1ac0e1eb44f9c820726d9db8c1198 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.691ms
2025-08-13 11:33:39.527 | INFO     | 924c65933daf4856987c0a39b72ca54d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:33:39.529 | INFO     | 924c65933daf4856987c0a39b72ca54d | 成功认证Java用户: pythontest
2025-08-13 11:33:39.534 | INFO     | 924c65933daf4856987c0a39b72ca54d | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:33:39.535 | INFO     | 924c65933daf4856987c0a39b72ca54d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:33:39.548 | INFO     | 924c65933daf4856987c0a39b72ca54d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:33:39.549 | INFO     | 924c65933daf4856987c0a39b72ca54d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 22.328ms
2025-08-13 11:52:33.543 | INFO     | 0d4dc273407543ae8304cb27609358b2 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:52:33.544 | INFO     | ee61b3420a7045f08a99283845a3540d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:52:33.545 | INFO     | c1738c93b473466fb9d7d5bf057f691d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:52:33.545 | INFO     | 0d4dc273407543ae8304cb27609358b2 | 成功认证Java用户: pythontest
2025-08-13 11:52:33.546 | INFO     | ee61b3420a7045f08a99283845a3540d | 成功认证Java用户: pythontest
2025-08-13 11:52:33.547 | INFO     | c1738c93b473466fb9d7d5bf057f691d | 成功认证Java用户: pythontest
2025-08-13 11:52:33.554 | INFO     | ee61b3420a7045f08a99283845a3540d | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:52:33.554 | INFO     | ee61b3420a7045f08a99283845a3540d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:52:33.556 | INFO     | c1738c93b473466fb9d7d5bf057f691d | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:52:33.557 | INFO     | c1738c93b473466fb9d7d5bf057f691d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:52:33.561 | INFO     | 0d4dc273407543ae8304cb27609358b2 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:52:33.562 | INFO     | 0d4dc273407543ae8304cb27609358b2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.871ms
2025-08-13 11:52:33.572 | INFO     | ee61b3420a7045f08a99283845a3540d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:52:33.574 | INFO     | c1738c93b473466fb9d7d5bf057f691d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:52:33.574 | INFO     | ee61b3420a7045f08a99283845a3540d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.510ms
2025-08-13 11:52:33.575 | INFO     | c1738c93b473466fb9d7d5bf057f691d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.508ms
2025-08-13 11:53:13.303 | INFO     | 5f42ae29585c47f9bc6e61210e16d118 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:13.305 | INFO     | 5f42ae29585c47f9bc6e61210e16d118 | 成功认证Java用户: pythontest
2025-08-13 11:53:13.316 | INFO     | 5f42ae29585c47f9bc6e61210e16d118 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:53:13.316 | INFO     | 5f42ae29585c47f9bc6e61210e16d118 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 13.164ms
2025-08-13 11:53:13.318 | INFO     | efd3ebe9eb58435e8ff4d527ddc2b616 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:13.319 | INFO     | efd3ebe9eb58435e8ff4d527ddc2b616 | 成功认证Java用户: pythontest
2025-08-13 11:53:13.324 | INFO     | efd3ebe9eb58435e8ff4d527ddc2b616 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:13.325 | INFO     | efd3ebe9eb58435e8ff4d527ddc2b616 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:53:13.335 | INFO     | efd3ebe9eb58435e8ff4d527ddc2b616 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:13.337 | INFO     | efd3ebe9eb58435e8ff4d527ddc2b616 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 18.015ms
2025-08-13 11:53:13.339 | INFO     | dfdfd2e77b2a4e79b6f7466cb5ea53c9 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:13.339 | INFO     | dfdfd2e77b2a4e79b6f7466cb5ea53c9 | 成功认证Java用户: pythontest
2025-08-13 11:53:13.343 | INFO     | dfdfd2e77b2a4e79b6f7466cb5ea53c9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:13.344 | INFO     | dfdfd2e77b2a4e79b6f7466cb5ea53c9 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:53:13.354 | INFO     | dfdfd2e77b2a4e79b6f7466cb5ea53c9 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:13.355 | INFO     | dfdfd2e77b2a4e79b6f7466cb5ea53c9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 16.923ms
2025-08-13 11:53:28.001 | INFO     | ab5c883cf39242c5b882398a2ba527cc | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:28.002 | INFO     | 2e95ad8f1c364934ad51698268696e9d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:28.003 | INFO     | ab5c883cf39242c5b882398a2ba527cc | 成功认证Java用户: pythontest
2025-08-13 11:53:28.005 | INFO     | 2e95ad8f1c364934ad51698268696e9d | 成功认证Java用户: pythontest
2025-08-13 11:53:28.010 | INFO     | 2e95ad8f1c364934ad51698268696e9d | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:28.010 | INFO     | 2e95ad8f1c364934ad51698268696e9d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:53:28.015 | INFO     | ab5c883cf39242c5b882398a2ba527cc | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:53:28.015 | INFO     | ab5c883cf39242c5b882398a2ba527cc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 14.262ms
2025-08-13 11:53:28.017 | INFO     | a60d2c9fdfb149058c32b861c9d78ae8 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:28.018 | INFO     | a60d2c9fdfb149058c32b861c9d78ae8 | 成功认证Java用户: pythontest
2025-08-13 11:53:28.021 | INFO     | 2e95ad8f1c364934ad51698268696e9d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:28.023 | INFO     | a60d2c9fdfb149058c32b861c9d78ae8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:28.023 | INFO     | a60d2c9fdfb149058c32b861c9d78ae8 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:53:28.025 | INFO     | 2e95ad8f1c364934ad51698268696e9d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.619ms
2025-08-13 11:53:28.034 | INFO     | a60d2c9fdfb149058c32b861c9d78ae8 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:28.035 | INFO     | a60d2c9fdfb149058c32b861c9d78ae8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 17.092ms
2025-08-13 11:53:52.520 | INFO     | c646a373ddf84a949b9ff669263d2fc0 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:52.522 | INFO     | c646a373ddf84a949b9ff669263d2fc0 | 成功认证Java用户: pythontest
2025-08-13 11:53:52.534 | INFO     | c646a373ddf84a949b9ff669263d2fc0 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:53:52.535 | INFO     | c646a373ddf84a949b9ff669263d2fc0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 14.655ms
2025-08-13 11:53:52.537 | INFO     | f468da4011054089995fc3b1302c891b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:52.538 | INFO     | f468da4011054089995fc3b1302c891b | 成功认证Java用户: pythontest
2025-08-13 11:53:52.543 | INFO     | f468da4011054089995fc3b1302c891b | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:52.543 | INFO     | f468da4011054089995fc3b1302c891b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:53:52.556 | INFO     | f468da4011054089995fc3b1302c891b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:52.557 | INFO     | f468da4011054089995fc3b1302c891b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 19.988ms
2025-08-13 11:53:52.558 | INFO     | 34b8d215614746c09f855c20bfd1afc1 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:52.559 | INFO     | 34b8d215614746c09f855c20bfd1afc1 | 成功认证Java用户: pythontest
2025-08-13 11:53:52.564 | INFO     | 34b8d215614746c09f855c20bfd1afc1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:52.564 | INFO     | 34b8d215614746c09f855c20bfd1afc1 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:53:52.577 | INFO     | 34b8d215614746c09f855c20bfd1afc1 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:52.578 | INFO     | 34b8d215614746c09f855c20bfd1afc1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 18.938ms
2025-08-13 11:53:56.627 | INFO     | f9d3d599230c4b829c6ece0c9473f655 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:56.627 | INFO     | 8c98f208c8c344a89f323584f6ef5537 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:56.628 | INFO     | 1045ccabe3d7450185040c90b979aa36 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-13 11:53:56.629 | INFO     | f9d3d599230c4b829c6ece0c9473f655 | 成功认证Java用户: pythontest
2025-08-13 11:53:56.630 | INFO     | 8c98f208c8c344a89f323584f6ef5537 | 成功认证Java用户: pythontest
2025-08-13 11:53:56.634 | INFO     | 1045ccabe3d7450185040c90b979aa36 | 成功认证Java用户: pythontest
2025-08-13 11:53:56.643 | INFO     | 8c98f208c8c344a89f323584f6ef5537 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:56.644 | INFO     | 8c98f208c8c344a89f323584f6ef5537 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-13 11:53:56.649 | INFO     | 1045ccabe3d7450185040c90b979aa36 | 获取用户权限成功: user_id=42, is_admin=False, permissions=17
2025-08-13 11:53:56.650 | INFO     | 1045ccabe3d7450185040c90b979aa36 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-13 11:53:56.654 | INFO     | f9d3d599230c4b829c6ece0c9473f655 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-13 11:53:56.658 | INFO     | f9d3d599230c4b829c6ece0c9473f655 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 31.451ms
2025-08-13 11:53:56.670 | INFO     | 8c98f208c8c344a89f323584f6ef5537 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:56.671 | INFO     | 1045ccabe3d7450185040c90b979aa36 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-13 11:53:56.673 | INFO     | 8c98f208c8c344a89f323584f6ef5537 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 45.631ms
2025-08-13 11:53:56.674 | INFO     | 1045ccabe3d7450185040c90b979aa36 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 46.600ms
